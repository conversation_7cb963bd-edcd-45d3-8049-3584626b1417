import darkStyle from '../../../assets/js/darkStyle.json';
export default {
  data: () => ({
    pointLineData: [],
    darkLabel:{
          color: 'white',
          backgroundColor: 'rgba(33, 33, 33, 0.5)',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          width: '200px',
          whiteSpace: 'normal',
          wordBreak: 'break-all',
          lineHeight: '16px'
        },
    whiteLabel:{
          color: 'black',
          backgroundColor: 'rgb(215,215,215)',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          width: '200px',
          whiteSpace: 'normal',
          wordBreak: 'break-all',
          lineHeight: '16px'
        }
  }),
  beforeDestroy() {
    // 清理流动动画
    this.stopFlowAnimation();
  },
  created() {
  },
  mounted() {
    this.initMapFn()
    this.getPointLineData();
  },
  methods: {
    // 初始化地图
    initMapFn() {
      this.map = new BMapGL.Map('allmap');
      const center = new BMapGL.Point(
        this.configEditing.mapCenter[0],
        this.configEditing.mapCenter[1]
      );
      this.map.centerAndZoom(center, this.configEditing.mapZoom);
      this.map.enableScrollWheelZoom(true);
      this.map.setTilt(45);

      // 切换地图样式->dark
      if (this.circuitName) this.changeMapStyle();

      // 监听地图加载完成
      this.map.addEventListener('tilesloaded', () => {
        this.mapLoaded = true;
        this.addMapLayers()
      });
    },

    // 获取点线数据
    getPointLineData() {
      this.$api.gis_data.getMapPointLineData().then(res => {
        this.pointLineData = res;
        this.addMapLayers();
      })
    },

    // 添加地图图层
    addMapLayers() {
      this.map.clearOverlays();
      // const point = new BMapGL.Point(108.9710, 34.2258);
      // // 创建自定义图标
      // const icon = new BMapGL.Icon(
      //   require('@/assets/images/icon_1.png'), // 图标路径
      //   new BMapGL.Size(32, 32), // 图标大小
      //   {
      //     anchor: new BMapGL.Size(16, 32), // 图标锚点
      //     imageOffset: new BMapGL.Size(0, 0) // 图标偏移
      //   }
      // );
      // const marker = new BMapGL.Marker(point
      //   // , { icon }
      // ); // 创建标注，并设置图标为自定义图标
      // this.map.addOverlay(marker);



      // 西安五个地标点的坐标和信息
      const landmarks = this.pointLineData

      // 渲染所有地标
      landmarks.forEach((landmark) => {
        const point = new BMapGL.Point(landmark.lng, landmark.lat);

        // 创建自定义图标（暂时注释）
        // const icon = new BMapGL.Icon(
        //   require('@/assets/images/icon_1.png'),
        //   new BMapGL.Size(32, 32),
        //   {
        //     anchor: new BMapGL.Size(16, 32),
        //     imageOffset: new BMapGL.Size(0, 0)
        //   }
        // );

        const marker = new BMapGL.Marker(point
          // , { icon: icon }
        );

        // 添加标签
        const label = new BMapGL.Label(`${landmark.name}<br>${landmark.description}`);
        // 判断有网元数据则使用darkLabel
        this.circuitName?label.setStyle(this.darkLabel):label.setStyle(this.whiteLabel);

        label.setOffset(new BMapGL.Size(10, -40));
        marker.setLabel(label);

        this.map.addOverlay(marker);
      });

      // 添加连接小寨和大雁塔的线段
      this.addConnectionLine();
    },

    // 添加连接线段的方法
    addConnectionLine() {
      // 从 landmarks 中查找小寨和大雁塔的坐标
      const landmarks = [
        { name: '小寨', lng: 108.9434, lat: 34.2178 },
        { name: '大雁塔', lng: 108.9646, lat: 34.2192 }
      ];
      // 折线拐点（不在 landmarks 数据中，仅用于折线渲染）
      const midPoint = { lng: 108.954, lat: 34.225 };
      const points = [
        new BMapGL.Point(landmarks[0].lng, landmarks[0].lat),
        new BMapGL.Point(midPoint.lng, midPoint.lat),
        new BMapGL.Point(landmarks[1].lng, landmarks[1].lat)
      ];
      const polyline = new BMapGL.Polyline(points, {
        // strokeColor: '#FF5722',    // 线条颜色：橙红色
        strokeColor: '#11a7efff',    // 线条颜色：蓝色
         
        strokeWeight: 3,           // 线条宽度
        strokeOpacity: 0.8,        // 线条透明度
        strokeStyle: 'solid'       // 线条样式：实线
      });

      // 添加线段到地图
      this.map.addOverlay(polyline);

      // 添加流动线效果
      // this.addFlowingLine();
    },
    
    // 添加流动线效果
    addFlowingLine() {
      // 使用 BMapGL 的动画线条效果
      const points = [
        new BMapGL.Point(108.9434, 34.2178), // 小寨
        new BMapGL.Point(108.954, 34.225),   // 中间点
        new BMapGL.Point(108.9646, 34.2192)  // 大雁塔
      ];

      // // 创建带动画效果的折线
      // const animatedPolyline = new BMapGL.Polyline(points, {
      //   strokeColor: '#00FF00',    // 绿色流动线
      //   strokeWeight: 4,           // 线条宽度
      //   strokeOpacity: 0.8,        // 线条透明度
      //   strokeStyle: 'dashed'      // 虚线样式
      // });

      // this.map.addOverlay(animatedPolyline);

      // 添加流动的小圆点
      this.addFlowingDots(points);
    },

    // 添加流动的圆点效果
    addFlowingDots(points) {
      this.flowingDots = [];
      this.flowAnimationId = null;

      // 计算路径上的所有位置点
      const pathPositions = this.calculatePathPositions(points, 50); // 50个位置点

      // 创建3个流动的圆点
      for (let i = 0; i < 3; i++) {
        const startIndex = i * 16; // 间隔分布
        const dotPoint = new BMapGL.Point(
          pathPositions[startIndex].lng,
          pathPositions[startIndex].lat
        );

        const flowDot = new BMapGL.Circle(dotPoint, 30, {
          strokeColor: '#00FF00',
          strokeWeight: 2,
          strokeOpacity: 1,
          fillColor: '#00FF00',
          fillOpacity: 0.9
        });

        this.map.addOverlay(flowDot);

        this.flowingDots.push({
          circle: flowDot,
          currentIndex: startIndex,
          positions: pathPositions
        });
      }

      // 启动流动动画
      this.startDotAnimation();
    },

    // 计算路径上的位置点
    calculatePathPositions(points, totalCount) {
      const positions = [];

      // 计算每段线的长度和总长度
      const segments = [];
      let totalLength = 0;

      for (let i = 0; i < points.length - 1; i++) {
        const start = points[i];
        const end = points[i + 1];
        const length = this.calculateDistance(start, end);
        segments.push({
          start,
          end,
          length,
          startDistance: totalLength
        });
        totalLength += length;
      }

      // 在线段上均匀分布位置点
      for (let i = 0; i < totalCount; i++) {
        const targetDistance = (totalLength / totalCount) * i;

        // 找到目标距离所在的线段
        let currentSegment = null;
        for (const segment of segments) {
          if (targetDistance >= segment.startDistance &&
              targetDistance <= segment.startDistance + segment.length) {
            currentSegment = segment;
            break;
          }
        }

        if (currentSegment) {
          // 计算在当前线段中的位置比例
          const segmentProgress = (targetDistance - currentSegment.startDistance) / currentSegment.length;

          // 插值计算位置
          const lng = currentSegment.start.lng +
                     (currentSegment.end.lng - currentSegment.start.lng) * segmentProgress;
          const lat = currentSegment.start.lat +
                     (currentSegment.end.lat - currentSegment.start.lat) * segmentProgress;

          positions.push({ lng, lat });
        }
      }

      return positions;
    },

    // 计算两点间距离（简化计算）
    calculateDistance(point1, point2) {
      const dlng = point2.lng - point1.lng;
      const dlat = point2.lat - point1.lat;
      return Math.sqrt(dlng * dlng + dlat * dlat);
    },

    // 启动圆点流动动画
    startDotAnimation() {
      const animate = () => {
        this.flowingDots.forEach(dot => {
          // 移动到下一个位置
          dot.currentIndex = (dot.currentIndex + 1) % dot.positions.length;
          const newPosition = dot.positions[dot.currentIndex];

          if (newPosition) {
            const newPoint = new BMapGL.Point(newPosition.lng, newPosition.lat);
            dot.circle.setCenter(newPoint);
          }
        });

        // 继续动画
        this.flowAnimationId = setTimeout(animate, 150); // 150ms间隔
      };

      animate();
    },

    // 停止流动动画
    stopFlowAnimation() {
      if (this.flowAnimationId) {
        clearTimeout(this.flowAnimationId);
        this.flowAnimationId = null;
      }
    },
    // 切换地图样式
    changeMapStyle() {
      this.map.setOptions({
        style: {
          styleJson: darkStyle
        },
        styleUrl: 'https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/mapstyle/mapstyle.json'
      });
    },

  }
}
