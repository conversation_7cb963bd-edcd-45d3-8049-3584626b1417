import darkStyle from '../../../assets/js/darkStyle.json';
export default {
  data: () => ({
    pointLineData: [],
    darkLabel:{
          color: 'white',
          backgroundColor: 'rgba(33, 33, 33, 0.5)',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          width: '200px',
          whiteSpace: 'normal',
          wordBreak: 'break-all',
          lineHeight: '16px'
        },
    whiteLabel:{
          color: 'black',
          backgroundColor: 'rgb(215,215,215)',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          width: '200px',
          whiteSpace: 'normal',
          wordBreak: 'break-all',
          lineHeight: '16px'
        }
  }),
  beforeDestroy() {
    // 清理流动动画
    this.stopFlowAnimation();
  },
  created() {
  },
  mounted() {
    this.initMapFn()
    this.getPointLineData();
  },
  methods: {
    // 初始化地图
    initMapFn() {
      this.map = new BMapGL.Map('allmap');
      const center = new BMapGL.Point(
        this.configEditing.mapCenter[0],
        this.configEditing.mapCenter[1]
      );
      this.map.centerAndZoom(center, this.configEditing.mapZoom);
      this.map.enableScrollWheelZoom(true);
      this.map.setTilt(45);

      // 切换地图样式->dark
      if (this.circuitName) this.changeMapStyle();

      // 监听地图加载完成
      this.map.addEventListener('tilesloaded', () => {
        this.mapLoaded = true;
        this.addMapLayers()
      });
    },

    // 获取点线数据
    getPointLineData() {
      this.$api.gis_data.getMapPointLineData().then(res => {
        this.pointLineData = res;
        this.addMapLayers();
      })
    },

    // 添加地图图层
    addMapLayers() {
      this.map.clearOverlays();
      // const point = new BMapGL.Point(108.9710, 34.2258);
      // // 创建自定义图标
      // const icon = new BMapGL.Icon(
      //   require('@/assets/images/icon_1.png'), // 图标路径
      //   new BMapGL.Size(32, 32), // 图标大小
      //   {
      //     anchor: new BMapGL.Size(16, 32), // 图标锚点
      //     imageOffset: new BMapGL.Size(0, 0) // 图标偏移
      //   }
      // );
      // const marker = new BMapGL.Marker(point
      //   // , { icon }
      // ); // 创建标注，并设置图标为自定义图标
      // this.map.addOverlay(marker);



      // 西安五个地标点的坐标和信息
      const landmarks = this.pointLineData

      // 渲染所有地标
      landmarks.forEach((landmark) => {
        const point = new BMapGL.Point(landmark.lng, landmark.lat);

        // 创建自定义图标（暂时注释）
        // const icon = new BMapGL.Icon(
        //   require('@/assets/images/icon_1.png'),
        //   new BMapGL.Size(32, 32),
        //   {
        //     anchor: new BMapGL.Size(16, 32),
        //     imageOffset: new BMapGL.Size(0, 0)
        //   }
        // );

        const marker = new BMapGL.Marker(point
          // , { icon: icon }
        );

        // 添加标签
        const label = new BMapGL.Label(`${landmark.name}<br>${landmark.description}`);
        // 判断有网元数据则使用darkLabel
        this.circuitName?label.setStyle(this.darkLabel):label.setStyle(this.whiteLabel);

        label.setOffset(new BMapGL.Size(10, -40));
        marker.setLabel(label);

        this.map.addOverlay(marker);
      });

      // 添加连接小寨和大雁塔的线段
      this.addConnectionLine();
    },

    // 添加连接线段的方法
    addConnectionLine() {
      // 从 landmarks 中查找小寨和大雁塔的坐标
      const landmarks = [
        { name: '小寨', lng: 108.9434, lat: 34.2178 },
        { name: '大雁塔', lng: 108.9646, lat: 34.2192 }
      ];
      // 折线拐点（不在 landmarks 数据中，仅用于折线渲染）
      const midPoint = { lng: 108.954, lat: 34.225 };
      const points = [
        new BMapGL.Point(landmarks[0].lng, landmarks[0].lat),
        new BMapGL.Point(midPoint.lng, midPoint.lat),
        new BMapGL.Point(landmarks[1].lng, landmarks[1].lat)
      ];
      const polyline = new BMapGL.Polyline(points, {
        // strokeColor: '#FF5722',    // 线条颜色：橙红色
        strokeColor: '#11a7efff',    // 线条颜色：蓝色
         
        strokeWeight: 3,           // 线条宽度
        strokeOpacity: 0.8,        // 线条透明度
        strokeStyle: 'solid'       // 线条样式：实线
      });

      // 添加线段到地图
      this.map.addOverlay(polyline);

      // 添加流动线效果
      // this.addFlowingLine();
    },
    
    // 添加流动线效果
    addFlowingLine() {
      // 使用 BMapGL 的动画线条效果
      const points = [
        new BMapGL.Point(108.9434, 34.2178), // 小寨
        new BMapGL.Point(108.954, 34.225),   // 中间点
        new BMapGL.Point(108.9646, 34.2192)  // 大雁塔
      ];

      // 创建流动效果的虚线
      const flowingPolyline = new BMapGL.Polyline(points, {
        strokeColor: '#00FF00',    // 绿色流动线
        strokeWeight: 4,           // 线条宽度
        strokeOpacity: 0.9,        // 线条透明度
        strokeStyle: 'dashed'      // 虚线样式
      });

      this.map.addOverlay(flowingPolyline);

      // 添加CSS动画样式来实现流动效果
      this.addFlowingAnimation(flowingPolyline);
    },

    // 添加流动动画样式
    addFlowingAnimation() {
      // 等待DOM渲染完成后添加动画
      this.$nextTick(() => {
        // 查找对应的SVG路径元素
        const mapContainer = document.getElementById('allmap');
        if (mapContainer) {
          const svgPaths = mapContainer.querySelectorAll('svg path');
          svgPaths.forEach(path => {
            const stroke = path.getAttribute('stroke');
            if (stroke && stroke.includes('0, 255, 0')) { // 绿色线条
              // 添加流动动画样式
              path.style.strokeDasharray = '10 5';
              path.style.strokeDashoffset = '0';
              path.style.animation = 'dash-flow 2s linear infinite';
            }
          });

          // 添加CSS动画关键帧
          this.addFlowingKeyframes();
        }
      });
    },

    // 添加CSS动画关键帧
    addFlowingKeyframes() {
      // 检查是否已经添加过样式
      if (document.getElementById('flowing-line-style')) {
        return;
      }

      const style = document.createElement('style');
      style.id = 'flowing-line-style';
      style.textContent = `
        @keyframes dash-flow {
          0% {
            stroke-dashoffset: 0;
          }
          100% {
            stroke-dashoffset: -15;
          }
        }
      `;
      document.head.appendChild(style);
    },

    // 停止流动动画（清理CSS样式）
    stopFlowAnimation() {
      const style = document.getElementById('flowing-line-style');
      if (style) {
        style.remove();
      }
    },
    // 切换地图样式
    changeMapStyle() {
      this.map.setOptions({
        style: {
          styleJson: darkStyle
        },
        styleUrl: 'https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/mapstyle/mapstyle.json'
      });
    },

  }
}
