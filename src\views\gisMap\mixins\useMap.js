export default {
  methods: {
    initMapFn() {
      this.map = new BMapGL.Map('allmap');
      const center = new BMapGL.Point(
        this.configEditing.mapCenter[0],
        this.configEditing.mapCenter[1]
      );
      this.map.centerAndZoom(center, this.configEditing.mapZoom);
      this.map.enableScrollWheelZoom(true);
      this.map.setTilt(45);
      // 监听地图加载完成
      this.map.addEventListener('tilesloaded', () => {
        this.mapLoaded = true;
        this.addMapLayers()
      });
      var styleOptions = {
        strokeColor: '#5E87DB',   // 边线颜色
        fillColor: '#5E87DB',     // 填充颜色。当参数为空时，圆形没有填充颜色
        strokeWeight: 2,          // 边线宽度，以像素为单位
        strokeOpacity: 1,         // 边线透明度，取值范围0-1
        fillOpacity: 0.2          // 填充透明度，取值范围0-1
      };
      var labelOptions = {
        borderRadius: '2px',
        background: '#FFFBCC',
        border: '1px solid #E1E1E1',
        color: '#703A04',
        fontSize: '12px',
        letterSpacing: '0',
        padding: '5px'
      };
      // // 实例化鼠标绘制工具
      this.drawingManager = new BMapGLLib.DrawingManager(this.map, {
        enableCalculate: false, // 绘制是否进行测距测面
        enableSorption: true,   // 是否开启边界吸附功能
        sorptiondistance: 20,   // 边界吸附距离
        circleOptions: styleOptions,     // 圆的样式
        polylineOptions: styleOptions,   // 线的样式
        polygonOptions: styleOptions,    // 多边形的样式
        rectangleOptions: styleOptions,  // 矩形的样式
        labelOptions: labelOptions,      // label样式
      });
    },


    // 添加地图图层
    addMapLayers() {
      this.map.clearOverlays();
      // const point = new BMapGL.Point(108.9710, 34.2258);
      // // 创建自定义图标
      // const icon = new BMapGL.Icon(
      //   require('@/assets/images/icon_1.png'), // 图标路径
      //   new BMapGL.Size(32, 32), // 图标大小
      //   {
      //     anchor: new BMapGL.Size(16, 32), // 图标锚点
      //     imageOffset: new BMapGL.Size(0, 0) // 图标偏移
      //   }
      // );
      // const marker = new BMapGL.Marker(point
      //   // , { icon }
      // ); // 创建标注，并设置图标为自定义图标
      // this.map.addOverlay(marker);



      // 西安五个地标点的坐标和信息
      const landmarks = [
        {
          name: '小寨',
          lng: 108.9434,
          lat: 34.2178,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        },
        {
          name: '钟楼',
          lng: 108.9402,
          lat: 34.2583,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        },
        {
          name: '大雁塔',
          lng: 108.9646,
          lat: 34.2192,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        },
        {
          name: '西安万象城',
          lng: 108.9563,
          lat: 34.2314,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        },
        {
          name: '西安北站',
          lng: 108.9633,
          lat: 34.3708,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        }
      ];

      // 渲染所有地标
      landmarks.forEach((landmark, index) => {
        const point = new BMapGL.Point(landmark.lng, landmark.lat);

        // 创建自定义图标
        const icon = new BMapGL.Icon(
          require('@/assets/images/icon_1.png'),
          new BMapGL.Size(32, 32),
          {
            anchor: new BMapGL.Size(16, 32),
            imageOffset: new BMapGL.Size(0, 0)
          }
        );

        const marker = new BMapGL.Marker(point
          // , { icon: icon }
        );

        // 添加标签
        const label = new BMapGL.Label(`${landmark.name}<br>${landmark.description}`);
        label.setStyle({
          color: 'black',
          backgroundColor: 'rgb(215,215,215)',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          width: '200px',
          whiteSpace: 'normal',
          wordBreak: 'break-all',
          lineHeight: '16px'
        });
        label.setOffset(new BMapGL.Size(10, -40));
        marker.setLabel(label);

        this.map.addOverlay(marker);
      });

      // 添加连接小寨和大雁塔的线段
      this.addConnectionLine();
    },

    // 添加连接线段的方法
    addConnectionLine() {
      // 从 landmarks 中查找小寨和大雁塔的坐标
      const landmarks = [
        { name: '小寨', lng: 108.9434, lat: 34.2178 },
        { name: '大雁塔', lng: 108.9646, lat: 34.2192 }
      ];
      // 折线拐点（不在 landmarks 数据中，仅用于折线渲染）
      const midPoint = { lng: 108.954, lat: 34.225 };
      const points = [
        new BMapGL.Point(landmarks[0].lng, landmarks[0].lat),
        new BMapGL.Point(midPoint.lng, midPoint.lat),
        new BMapGL.Point(landmarks[1].lng, landmarks[1].lat)
      ];
      const polyline = new BMapGL.Polyline(points, {
        strokeColor: '#FF5722',    // 线条颜色：橙红色
        strokeWeight: 3,           // 线条宽度
        strokeOpacity: 0.8,        // 线条透明度
        strokeStyle: 'solid'       // 线条样式：实线
      });

      // 添加线段到地图
      this.map.addOverlay(polyline);

      // 添加蝌蚪线（箭头）效果
      this.addArrowsOnLine(points);
    },

    // 在线段上添加箭头标记（蝌蚪线效果）
    addArrowsOnLine(points) {
      // 计算线段上的箭头位置
      const arrowPositions = this.calculateArrowPositions(points, 6); // 6个箭头

      arrowPositions.forEach((position) => {
        const arrowPoint = new BMapGL.Point(position.lng, position.lat);

        // 根据角度选择合适的箭头符号
        let arrowSymbol = '→';
        if (position.angle > -45 && position.angle <= 45) {
          arrowSymbol = '→'; // 向右
        } else if (position.angle > 45 && position.angle <= 135) {
          arrowSymbol = '↓'; // 向下
        } else if (position.angle > 135 || position.angle <= -135) {
          arrowSymbol = '←'; // 向左
        } else {
          arrowSymbol = '↑'; // 向上
        }

        // 创建箭头标签
        const arrowLabel = new BMapGL.Label(arrowSymbol, {
          position: arrowPoint
        });

        arrowLabel.setStyle({
          color: '#FF5722',
          fontSize: '14px',
          fontWeight: 'bold',
          border: 'none',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderRadius: '50%',
          width: '20px',
          height: '20px',
          textAlign: 'center',
          lineHeight: '20px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
        });

        arrowLabel.setOffset(new BMapGL.Size(-10, -10));
        this.map.addOverlay(arrowLabel);
      });
    },

    // 计算箭头在线段上的位置
    calculateArrowPositions(points, arrowCount) {
      const positions = [];

      // 计算每段线的长度和总长度
      const segments = [];
      let totalLength = 0;

      for (let i = 0; i < points.length - 1; i++) {
        const start = points[i];
        const end = points[i + 1];
        const length = this.calculateDistance(start, end);
        segments.push({
          start,
          end,
          length,
          startDistance: totalLength
        });
        totalLength += length;
      }

      // 在线段上均匀分布箭头
      for (let i = 1; i <= arrowCount; i++) {
        const targetDistance = (totalLength / (arrowCount + 1)) * i;

        // 找到目标距离所在的线段
        let currentSegment = null;
        for (const segment of segments) {
          if (targetDistance >= segment.startDistance &&
              targetDistance <= segment.startDistance + segment.length) {
            currentSegment = segment;
            break;
          }
        }

        if (currentSegment) {
          // 计算在当前线段中的位置比例
          const segmentProgress = (targetDistance - currentSegment.startDistance) / currentSegment.length;

          // 插值计算箭头位置
          const lng = currentSegment.start.lng +
                     (currentSegment.end.lng - currentSegment.start.lng) * segmentProgress;
          const lat = currentSegment.start.lat +
                     (currentSegment.end.lat - currentSegment.start.lat) * segmentProgress;

          // 计算箭头角度
          const angle = this.calculateAngle(currentSegment.start, currentSegment.end);

          positions.push({ lng, lat, angle });
        }
      }

      return positions;
    },

    // 计算两点间距离（简化计算）
    calculateDistance(point1, point2) {
      const dlng = point2.lng - point1.lng;
      const dlat = point2.lat - point1.lat;
      return Math.sqrt(dlng * dlng + dlat * dlat);
    },

    // 计算两点间的角度
    calculateAngle(start, end) {
      const dlng = end.lng - start.lng;
      const dlat = end.lat - start.lat;
      return Math.atan2(dlat, dlng) * 180 / Math.PI;
    },

    // 创建箭头SVG图标
    createArrowSVG(angle) {
      const svg = `
        <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
          <g transform="rotate(${angle} 8 8)">
            <path d="M2 8 L8 4 L8 6 L14 6 L14 10 L8 10 L8 12 Z"
                  fill="#FF5722"
                  stroke="#FF5722"
                  stroke-width="1"/>
          </g>
        </svg>
      `;

      // 将SVG转换为Data URL
      return 'data:image/svg+xml;base64,' + btoa(encodeURIComponent(svg));
    }

  }
}
