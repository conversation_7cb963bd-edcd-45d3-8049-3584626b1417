<template>
  <div class="app-container">
    <el-row class="container1">
      <!-- 使用 split-pane 实现可拖拽分割 -->
      <split-pane :min-percent="10" :default-percent="15" split="vertical">
        <template slot="paneL">
          <div class="muen-left" v-loading="loading">
            <div class="avue-sidebar" @click.stop>
              <el-input 
                placeholder="请输入电路名称" 
                v-model="filterText" 
                popper-class="search-input"
                clearable
                @keyup.enter.native="searchHandler">
                <i slot="suffix" class="el-input__icon el-icon-search" @click="searchHandler"></i>
              </el-input>
              <div class="tree-cls" style="margin-top: 16px">
                <el-tree
                  class="filter-tree"
                  :data="treeData"
                  :props="defaultProps"
                  node-key="circuitCode"
                  :highlight-current="highlightBd"
                  default-expand-all
                  :expand-on-click-node="false"
                  :filter-node-method="filterNode"
                  @node-click="handleNodeClick"
                  ref="tree">
                </el-tree>
              </div>
            </div>
          </div>
        </template>
        <template slot="paneR">
          <div class="content_right">
            <div class="nc-query" v-loading="loading">
              <h2 style="color:#fff">{{firstCircuitName}}</h2>
              <iframe
              :src="url" 
              title=""
              style="width: 100%;height: 100%;border: none;"></iframe>
              
              <!-- 结果展示区域 -->
              <!-- <div v-if="collectedCircuitNames.length > 0" class="result-display">
                <h3>当前节点下的所有电路名称:</h3>
                <ul>
                  <li v-for="(name, index) in collectedCircuitNames" :key="index">{{ name }}</li>
                </ul>
              </div> -->
            </div>
          </div>
        </template>
      </split-pane>
    </el-row>
  </div>
</template>

<script>
import SplitPane from 'vue-splitpane'
import Topo from './topo/index.vue'
import {datas} from "@/assets/img/topo/topoIcon.js";
export default {
  name: "ResourceTopologyTop",
  components: { Topo ,SplitPane},
  props: {
    classId: {
      type: String,
      default: "spcRoom",
    },
    tableTitle: {
      type: String,
      default: "资源",
    },
  },
  watch: {
    cols: {
      handler(n, o) { },
      deep: true,
    },
    filterText(val) {
        this.$refs.tree.filter(val);
      },
  },
  data() {
    return {
      highlightBd: true,
      loading: false,
      filterText: '',
      treeExpandData: [1],
      treeData: [
        {
        id: 1,
        label: '全局电路',
        url:"http://************:8183/#/topology_component/ShannxiCoreNetTopologyIndex?type=SXHXWJKTPT-202504101111&resourceTopology=true&isLogin=false"
      }, 
      {
        id: 2,
        label: '业务电路',
        children: []
      }
      ],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'leaf'
      },
      firstCircuitName:'',
      url: '',
      overallUrl:"http://************:8183/#/topology_component/ShannxiCoreNetTopologyIndex?type=SXHXWJKTPT-202504101111&resourceTopology=true&isLogin=false",
      businessUrl:'http://************:8183/#/topology_component/ShannxiCoreNetEndToEndTopologyIndex?type=SXDLTP-************&dataType=TOPO_CIRCUIT_DOMAIN_PHY_LINK|name||2b0619c9-e218-4578-97ab-eb0a21f79897|&dataId=12301018&isLogin=false',
      collectedCircuitNames: [] // 存储收集到的电路名称
    };
  },
  created() {
    //初始化查询配置 和tab展示列
        this.getTopoDatas()
        
  },
  mounted() {
  },
  methods: {
    filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
    async getTopoDatas(){
        const res = await this.$api.transApi.getSpecialCircuit();
        this.firstCircuitName = res.data[0].circuitName
        let treeData = this.transformToTree(res.data)
        
        this.treeData = treeData;
        this.$nextTick(() => {
          if (this.$route.query.circuitName) {
            this.firstCircuitName = this.$route.query.circuitName;
            this.filterText = this.$route.query.circuitName;
          }
          let circuitName = []
          if (this.$route.query.circuitName) {
            circuitName = [
          {
              "classId": "TOPO_CORE_LINK|||2b0619c9-e218-4578-97ab-eb0a21f79897|",
              "option": {
                  "uuid": {
                      "$in": this.$route.query.circuitName
                  }
              }
          }
      ]
            this.url = `http://************:8183/#/topology_component/ShanxiCoreEndToEndIndex?type=SXDLTP-************&dataType=&dataId=${JSON.stringify(circuitName)}&isLogin=false`;
            // this.url = `http://************:8183/#/topology_component/ShannxiCoreNetEndToEndTopologyIndex?type=SXDLTP-************&dataType=TOPO_CORE_LINK|||2b0619c9-e218-4578-97ab-eb0a21f79897|&dataId=${this.$route.query.circuitName}&isLogin=false`
          } else {
            circuitName = [
          {
              "classId": "TOPO_CORE_LINK|||2b0619c9-e218-4578-97ab-eb0a21f79897|",
              "option": {
                  "uuid": {
                      "$in": this.firstCircuitName
                  }
              }
          }
      ]
            this.url = `http://************:8183/#/topology_component/ShanxiCoreEndToEndIndex?type=SXDLTP-************&dataType=&dataId=${JSON.stringify(circuitName)}&isLogin=false`;
            // this.url = `http://************:8183/#/topology_component/ShannxiCoreNetEndToEndTopologyIndex?type=SXDLTP-************&dataType=TOPO_CORE_LINK|||2b0619c9-e218-4578-97ab-eb0a21f79897|&dataId=${this.firstCircuitName}&isLogin=false`
          }
        });
    },
    // 将扁平数组转换为树形结构
  transformToTree(data) {
    const tree = [];
    const busMap = {}; // 按busName分组
    
    // 第一层分组：按busName
    data.forEach(item => {
      if (!busMap[item.busName]) {
        busMap[item.busName] = {
          id: `bus_${item.busName}`,
          label: item.busName,
          level: 'bus',
          children: []
        };
      }
      
      // 第二层分组：按groupNumber
      const busNode = busMap[item.busName];
      
      let groupNode = busNode.children.find(g => g.label === (item.groupNumber ? item.groupNumber.toString() : '默认分组'));
      
      if (!groupNode) {
        groupNode = {
          id: `group_${item.busName}_${item.groupNumber}`,
          label: item.groupNumber?item.groupNumber.toString():'默认分组',
          level: 'group',
          children: []
        };
        busNode.children.push(groupNode);
      }
      
      // 添加原始数据节点
      groupNode.children.push({
        ...item,
        id: item.id || `${item.busName}_${item.groupNumber}_${item.portName}`,
        label: item.circuitName || item.name,
        // url:`http://************:8183/#/topology_component/ShannxiCoreNetEndToEndTopologyIndex?type=SXDLTP-************&dataType=TOPO_CORE_LINK|||2b0619c9-e218-4578-97ab-eb0a21f79897|&dataId=${item.circuitName}&isLogin=false`,
        level: 'item'
      });
      
    });
    
    // 转换为数组形式
    return Object.values(busMap);
  },
    handleNodeClick(msg){
      let circuitName = []
      console.log('点击的节点信息:', msg);
      if (msg.children && msg.children.length > 0) {
        const circuitNames = this.getCircuitNamesFromNode(msg);
        console.log('当前节点下的所有circuitName:', circuitNames);
        circuitName = [
          {
              "classId": "TOPO_CORE_LINK|||2b0619c9-e218-4578-97ab-eb0a21f79897|",
              "option": {
                  "uuid": {
                      "$in": circuitNames.toString()
                  }
              }
          }
      ]
        // 存储结果并更新视图
        this.collectedCircuitNames = circuitNames;
      } else {
        circuitName = [
          {
              "classId": "TOPO_CORE_LINK|||2b0619c9-e218-4578-97ab-eb0a21f79897|",
              "option": {
                  "uuid": {
                      "$in": msg.circuitName
                  }
              }
          }
      ]
      }
        this.url = `http://************:8183/#/topology_component/ShanxiCoreEndToEndIndex?type=SXDLTP-************&dataType=&dataId=${JSON.stringify(circuitName)}&isLogin=false`;
        console.log(this.url);
        
        this.firstCircuitName = msg.circuitName || msg.label;
      
    },
    
    // 递归获取节点下所有circuitName
    getCircuitNamesFromNode(node) {
      const circuitNames = [];
      
      // 辅助递归函数
      const collectNames = (currentNode) => {
        // 如果是叶子节点且有circuitName，添加到结果中
        if (currentNode.level === 'item' && currentNode.circuitName) {
          circuitNames.push(currentNode.circuitName);
        }
        
        // 递归处理子节点
        if (currentNode.children && currentNode.children.length > 0) {
          currentNode.children.forEach(child => {
            collectNames(child);
          });
        }
      };
      
      // 开始从指定节点收集
      collectNames(node);
      return circuitNames;
    },
    
    searchHandler(){}
  },
};
</script>

<style lang="less" scoped>
.app-container {
  overflow: hidden;
  height: calc(100vh - 100px);
  display: flex;

  .container1 {
    display: flex;
    flex: 1;
    padding: 24px;
    height: 100%;
    min-height: 840px;
    width: 100%;

    // 左侧菜单样式调整
    .muen-left {
      height: 100%;
      padding: 24px;
      margin-right: 16px;
      background: url("../../assets/images/menu_left.png") center center no-repeat;
      background-size: 100% 100%;

      .avue-sidebar {
        .tree-cls {
          max-height: 700px;
          overflow: auto;
        }

        :deep(.el-input__inner) {
          border-radius: 4px;
          border: 1px solid rgba(37, 190, 247, 0.5);
          -webkit-appearance: none;
          background-color: transparent;
          background-image: none;
          box-sizing: border-box;
          color: #AFC5F1;
          display: inline-block;
          font-size: inherit;
          height: 40px;
          line-height: 40px;
          outline: 0;
          padding: 0 12px;
          transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
        }

        .filter-tree {
          color: #fff;
          width: 500px;
          background-color: transparent;
          :deep(.el-tree-node__content):hover {
            background-color: rgba(22, 155, 250, .1) !important;
          }

          ::v-deep .el-tree-node:focus>.el-tree-node__content {
            background-color: rgba(22, 155, 250, .1) !important;
          }

          :deep(.el-tree-node__content) {
            background-color: transparent;
          }
        }
      }





      /* 引入上面定义的滚动条样式 */
      ::-webkit-scrollbar {
        width: 10px;
        background-color: #083f69;
      }

      ::-webkit-scrollbar-track {
        background: #062440;
      }

      ::-webkit-scrollbar-thumb {
        background: #1659A6;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
    }
  }



  .menu-bg {
    background: url("../../assets/images/menu-bg-yes.png") center center no-repeat;
    background-size: 100% 100%;

    height: 48px;
    // width: 255px;
    display: flex;
    text-align: center;
    color: #000a31;
    margin: 29px 25px 10px 25px;
    background-size: 100% 100%;
    background-color: transparent;
    align-items: center;
    justify-content: space-around;
    font-family: PingFangSC-Medium;
  }

  ::v-deep {
    .el-menu {
      border-right: none;
      margin: 0 25px 0 25px;

      .el-menu-item {
        color: #fff;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        text-align: center;
      }

      .el-menu-item.is-active {
        color: #fff;
        text-align: center;
        background-color: rgba(37, 190, 247, 0.5);
        font-family: PingFangSC-Regular;
        font-size: 16px;
        border-right: 2px solid #169bfa;
      }

      .el-col {
        float: left;
      }
    }
  }

  .content_right {
    background: url("../../assets/images/content_right.png") center center no-repeat;
    background-size: 100% 100%;
    display: flex;
    height: 100%;
    // float: right;
    padding: 16px;
    flex: 1;
  }

  .table-title {
    color: #ffffff;
    font-size: 18px;
    line-height: 40px;

    &::before {
      content: "";
      width: 3px;
      height: 16px;
      vertical-align: middle;
      display: inline-flex;
      background: #ffffff;
      margin: 10px 8px 13px 0;
      box-shadow: 0px 0px 5px 0px #ffffff;
    }
  }

  .nc-query {
    border-radius: 2px;
    // padding: 24px 16px 12px;
    margin: 0 0 20px 0;
    width: 100%;
  }

  .formItem {
    float: left;
    width: 20%;
  }

  .page-list {
    text-align: right;
    width: 100%;
    color: #5299c9;
    margin-top: 20px;
  }



  ::v-deep .el-pagination__total {
    color: #afc5f1;
  }

  // 拖拽条样式
::v-deep .splitter-pane-resizer.vertical {
  width: 5px;
  background: rgba(37, 190, 247, 0.6);
  cursor: col-resize;
  &:hover {
    background: rgba(37, 190, 247, 0.5);
  }
}
::v-deep .vue-splitter-container {
  width: 100%;
}
::v-deep .splitter-pane-resizer.vertical{
  margin-left: -13px;
}

  // 结果展示样式
  .result-display {
    background-color: rgba(6, 36, 64, 0.8);
    border-radius: 8px;
    padding: 16px;
    color: #afc5f1;
    margin-top: 16px;
    border: 1px solid rgba(37, 190, 247, 0.5);
  }

  .result-display h3 {
    color: #25beff;
    margin-bottom: 10px;
    font-size: 16px;
  }

  .result-display ul {
    padding-left: 20px;
  }

  .result-display li {
    margin-bottom: 5px;
  }
}
/deep/.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: rgba(22, 155, 250, 0.3) !important;
  color: rgba(22, 155, 250, 1);
}
</style>

<style lang="less">
.el-loading-mask {
z-index: 998;
  background: rgba(5, 99, 153, 0.3);
}

.filter-tree {
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(22, 155, 250, 0.3) !important;
    .el-tree-node__label {
      color: #25beff;
      font-weight: bold;
    }
  }
}
</style>