<svg xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256">
  <!-- 背景渐变定义 -->
  <defs>
    <radialGradient id="centerGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#0080ff;stop-opacity:0.4"/>
      <stop offset="100%" style="stop-color:#004080;stop-opacity:0.1"/>
    </radialGradient>

    <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.2"/>
      <stop offset="50%" style="stop-color:#00ffff;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#00ffff;stop-opacity:0.2"/>
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="256" height="256" fill="url(#centerGlow)"/>

  <!-- 中心圆形区域 -->
  <circle cx="128" cy="128" r="40" fill="none" stroke="#00ffff" stroke-width="2" opacity="0.8"/>

  <!-- 脉冲圆环动画 -->
  <circle cx="128" cy="128" r="20" fill="none" stroke="#00ffff" stroke-width="3" opacity="1">
    <animate attributeName="r" values="20;60;20" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="1;0.2;1" dur="2s" repeatCount="indefinite"/>
  </circle>

  <circle cx="128" cy="128" r="20" fill="none" stroke="#47bce8" stroke-width="2" opacity="1">
    <animate attributeName="r" values="20;80;20" dur="3s" repeatCount="indefinite" begin="0.5s"/>
    <animate attributeName="opacity" values="1;0.1;1" dur="3s" repeatCount="indefinite" begin="0.5s"/>
  </circle>

  <!-- 连接线 - 向上 -->
  <line x1="128" y1="88" x2="128" y2="20" stroke="url(#lineGradient)" stroke-width="3">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
  </line>

  <!-- 连接线 - 向下 -->
  <line x1="128" y1="168" x2="128" y2="236" stroke="url(#lineGradient)" stroke-width="3">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.3s"/>
  </line>

  <!-- 连接线 - 向左 -->
  <line x1="88" y1="128" x2="20" y2="128" stroke="url(#lineGradient)" stroke-width="3">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>
  </line>

  <!-- 连接线 - 向右 -->
  <line x1="168" y1="128" x2="236" y2="128" stroke="url(#lineGradient)" stroke-width="3">
    <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite" begin="0.9s"/>
  </line>

  <!-- 流动粒子效果 -->
  <circle r="3" fill="#00ffff" opacity="0.8">
    <animateMotion dur="2s" repeatCount="indefinite" path="M128,88 L128,20"/>
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
  </circle>

  <circle r="3" fill="#00ffff" opacity="0.8">
    <animateMotion dur="2s" repeatCount="indefinite" path="M128,168 L128,236" begin="0.5s"/>
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0.5s"/>
  </circle>

  <circle r="3" fill="#00ffff" opacity="0.8">
    <animateMotion dur="2s" repeatCount="indefinite" path="M88,128 L20,128" begin="1s"/>
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="1s"/>
  </circle>

  <circle r="3" fill="#00ffff" opacity="0.8">
    <animateMotion dur="2s" repeatCount="indefinite" path="M168,128 L236,128" begin="1.5s"/>
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="1.5s"/>
  </circle>

  <!-- 中心核心圆 -->
  <circle cx="128" cy="128" r="15" fill="#00ffff" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="1s" repeatCount="indefinite"/>
  </circle>

  <!-- 中心点 -->
  <circle cx="128" cy="128" r="5" fill="#ffffff" opacity="1"/>
</svg>